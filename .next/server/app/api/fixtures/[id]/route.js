"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/fixtures/[id]/route";
exports.ids = ["app/api/fixtures/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_leduyanh_Downloads_sport_APISportsGamev2_FECMS_src_app_api_fixtures_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/fixtures/[id]/route.ts */ \"(rsc)/./src/app/api/fixtures/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/fixtures/[id]/route\",\n        pathname: \"/api/fixtures/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/fixtures/[id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Downloads/sport/APISportsGamev2-FECMS/src/app/api/fixtures/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_leduyanh_Downloads_sport_APISportsGamev2_FECMS_src_app_api_fixtures_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/fixtures/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/fixtures/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/fixtures/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst dynamic = \"force-dynamic\";\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const fixtureId = params.id;\n        console.log(\"\\uD83D\\uDD04 Proxying fixture detail request:\", `${API_BASE_URL}/football/fixtures/${fixtureId}`);\n        const response = await fetch(`${API_BASE_URL}/football/fixtures/${fixtureId}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch fixture\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Fixture detail fetched successfully:\", data.data?.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request, { params }) {\n    try {\n        const fixtureId = params.id;\n        const body = await request.json();\n        console.log(\"\\uD83D\\uDD04 Proxying fixture update request:\", `${API_BASE_URL}/football/fixtures/${fixtureId}`);\n        const response = await fetch(`${API_BASE_URL}/football/fixtures/${fixtureId}`, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update fixture\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Fixture updated successfully:\", data.data?.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const fixtureId = params.id;\n        console.log(\"\\uD83D\\uDD04 Proxying fixture delete request:\", `${API_BASE_URL}/football/fixtures/${fixtureId}`);\n        const response = await fetch(`${API_BASE_URL}/football/fixtures/${fixtureId}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to delete fixture\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        console.log(\"✅ Fixture deleted successfully:\", fixtureId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Fixture deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/fixtures/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffixtures%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fleduyanh%2FDownloads%2Fsport%2FAPISportsGamev2-FECMS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();