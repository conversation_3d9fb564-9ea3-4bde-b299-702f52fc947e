{"version": 3, "sources": ["../../src/cli/next-info.ts"], "names": ["nextInfo", "dir", "process", "cwd", "getPackageVersion", "packageName", "require", "version", "getNextConfig", "config", "loadConfig", "PHASE_INFO", "output", "experimental", "useWasmBinary", "getBinaryVersion", "binaryName", "childProcess", "execFileSync", "toString", "trim", "printHelp", "console", "log", "cyan", "printDefaultInfo", "installedRelease", "nextConfig", "os", "platform", "arch", "versions", "node", "res", "fetch", "releases", "json", "newestRelease", "tag_name", "replace", "warn", "yellow", "bold", "e", "message", "runSharedDependencyCheck", "tools", "skipMessage", "getSupportedArchTriples", "currentPlatform", "spawn", "triples", "availableTools", "tool", "check", "sync", "bin", "checkArgs", "status", "push", "length", "messages", "result", "outputs", "triple", "triplePkgName", "platformArchABI", "resolved", "resolve", "proc", "args", "procPromise", "Promise", "stdout", "on", "data", "stderr", "c", "code", "join", "printVerbose", "fs", "tasks", "title", "scripts", "default", "isWsl", "ciInfo", "is<PERSON>ock<PERSON>", "isCI", "name", "report", "getReport", "header", "javascriptHeap", "sharedObjects", "commandLine", "host", "cpus", "networkInterfaces", "reportSummary", "JSON", "stringify", "platformArchTriples", "loadBindings", "bindings", "target", "getTargetTriple", "path", "fallbackBindingsDirectory", "nextPath", "dirname", "tryResolve", "pkgName", "fileExists", "existsSync", "loadError", "loadSuccess", "linux", "win32", "darwin", "task", "targetPlatform", "undefined", "taskScript", "taskResult"], "mappings": ";;;;;+BAklBSA;;;eAAAA;;;2DAhlBM;sEACU;4BAEU;2BAER;+DACJ;;;;;;AAEvB,MAAMC,MAAMC,QAAQC,GAAG;AAsCvB,SAASC,kBAAkBC,WAAmB;IAC5C,IAAI;QACF,OAAOC,QAAQ,CAAC,EAAED,YAAY,aAAa,CAAC,EAAEE,OAAO;IACvD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,eAAeC;QAMMC;IALnB,MAAMA,SAAS,MAAMC,IAAAA,eAAU,EAACC,qBAAU,EAAEV;IAE5C,OAAO;QACLW,QAAQH,OAAOG,MAAM,IAAI;QACzBC,cAAc;YACZC,aAAa,GAAEL,uBAAAA,OAAOI,YAAY,qBAAnBJ,qBAAqBK,aAAa;QACnD;IACF;AACF;AAEA;;;CAGC,GACD,SAASC,iBAAiBC,UAAkB;IAC1C,IAAI;QACF,OAAOC,sBAAY,CAChBC,YAAY,CAACF,YAAY;YAAC;SAAY,EACtCG,QAAQ,GACRC,IAAI;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,SAASC;IACPC,QAAQC,GAAG,CACT,CAAC;;;;;;;;;;;YAWO,EAAEC,IAAAA,gBAAI,EAAC,kDAAkD,CAAC;AAEtE;AAEA;;CAEC,GACD,eAAeC;IACb,MAAMC,mBAAmBtB,kBAAkB;IAC3C,MAAMuB,aAAa,MAAMnB;IAEzBc,QAAQC,GAAG,CAAC,CAAC;;YAEH,EAAEK,WAAE,CAACC,QAAQ,GAAG;QACpB,EAAED,WAAE,CAACE,IAAI,GAAG;WACT,EAAEF,WAAE,CAACrB,OAAO,GAAG;;QAElB,EAAEL,QAAQ6B,QAAQ,CAACC,IAAI,CAAC;OACzB,EAAEjB,iBAAiB,OAAO;QACzB,EAAEA,iBAAiB,QAAQ;QAC3B,EAAEA,iBAAiB,QAAQ;;QAE3B,EAAEW,iBAAiB;sBACL,EAAEtB,kBAAkB,sBAAsB;SACvD,EAAEA,kBAAkB,SAAS;aACzB,EAAEA,kBAAkB,aAAa;cAChC,EAAEA,kBAAkB,cAAc;;UAEtC,EAAEuB,WAAWf,MAAM,CAAC;;AAE9B,CAAC;IAEC,IAAI;QACF,MAAMqB,MAAM,MAAMC,MAChB;QAEF,MAAMC,WAAW,MAAMF,IAAIG,IAAI;QAC/B,MAAMC,gBAAgBF,QAAQ,CAAC,EAAE,CAACG,QAAQ,CAACC,OAAO,CAAC,MAAM;QAEzD,IAAIb,qBAAqBW,eAAe;YACtCf,QAAQkB,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EACPC,IAAAA,gBAAI,EAAC,SACL,mDAAmD,EAAEhB,iBAAiB,YAAY,EAAEW,cAAc;;qEAEvC,CAAC;QAElE;IACF,EAAE,OAAOM,GAAG;QACVrB,QAAQkB,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EACPC,IAAAA,gBAAI,EAAC,SACL,oDAAoD,EACpD,AAACC,EAAYC,OAAO,CACrB;gBACS,EAAElB,iBAAiB;;mEAEgC,CAAC;IAElE;AACF;AAEA;;;;;CAKC,GACD,eAAemB,yBACbC,KAA4E,EAC5EC,WAAmB;QAKHC;IAHhB,MAAMC,kBAAkBrB,WAAE,CAACC,QAAQ;IACnC,MAAMqB,QAAQ5C,QAAQ;IACtB,MAAM,EAAE0C,uBAAuB,EAAE,GAAG1C,QAAQ;IAC5C,MAAM6C,UAAUH,EAAAA,2CAAAA,yBAAyB,CAACC,gBAAgB,qBAA1CD,wCAA4C,CAACpB,WAAE,CAACE,IAAI,GAAG,KAAI,EAAE;IAC7E,mFAAmF;IAEnF,MAAMsB,iBAAiB,EAAE;IACzB,KAAK,MAAMC,QAAQP,MAAO;QACxB,IAAI;YACF,MAAMQ,QAAQJ,MAAMK,IAAI,CAACF,KAAKG,GAAG,EAAEH,KAAKI,SAAS;YACjD,IAAIH,MAAMI,MAAM,KAAK,GAAG;gBACtBN,eAAeO,IAAI,CAACN;YACtB;QACF,EAAE,OAAM;QACN,kCAAkC;QACpC;IACF;IAEA,IAAID,eAAeQ,MAAM,KAAK,GAAG;QAC/B,OAAO;YACLC,UAAUd;YACVe,QAAQ;QACV;IACF;IAEA,MAAMC,UAAyB,EAAE;IACjC,IAAID,SAA0B;IAE9B,KAAK,MAAME,UAAUb,QAAS;QAC5B,MAAMc,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,CAAC,CAAC;QAC3D,IAAIC;QACJ,IAAI;YACFA,WAAW7D,QAAQ8D,OAAO,CAACH;QAC7B,EAAE,OAAOtB,GAAG;YACV,OAAO;gBACLkB,UACE;gBACFC,QAAQ;YACV;QACF;QAEA,KAAK,MAAMT,QAAQD,eAAgB;YACjC,MAAMiB,OAAOnB,MAAMG,KAAKG,GAAG,EAAE;mBAAIH,KAAKiB,IAAI;gBAAEH;aAAS;YACrDJ,QAAQJ,IAAI,CAAC,CAAC,QAAQ,EAAEN,KAAKG,GAAG,CAAC,eAAe,CAAC;YACjD,yFAAyF;YACzF,MAAMe,cAAc,IAAIC,QAAQ,CAACJ;gBAC/BC,KAAKI,MAAM,CAACC,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQJ,IAAI,CAACgB;gBACf;gBACAN,KAAKO,MAAM,CAACF,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQJ,IAAI,CAACgB;gBACf;gBACAN,KAAKK,EAAE,CAAC,SAAS,CAACG,IAAWT,QAAQS;YACvC;YAEA,IAAIC,OAAO,MAAMP;YACjB,IAAIO,SAAS,GAAG;gBACdhB,SAAS;YACX;QACF;IACF;IAEA,OAAO;QACLlD,QAAQmD,QAAQgB,IAAI,CAAC;QACrBjB;IACF;AACF;AAEA;;CAEC,GACD,eAAekB;IACb,MAAMC,KAAK3E,QAAQ;IACnB,MAAM2C,kBAAkBrB,WAAE,CAACC,QAAQ;IAEnC,IACEoB,oBAAoB,WACpBA,oBAAoB,WACpBA,oBAAoB,UACpB;QACA3B,QAAQC,GAAG,CACT;QAEF;IACF;IAEA,wBAAwB;IACxB,MAAM2D,QAKD;QACH;YACEC,OAAO;YACPC,SAAS;gBACPC,SAAS;oBACP,+FAA+F;oBAC/F,2CAA2C;oBAC3C,MAAMC,QAAQhF,QAAQ;oBACtB,MAAMiF,SAASjF,QAAQ;oBACvB,MAAMkF,WAAWlF,QAAQ;oBAEzB,MAAMM,SAAS,CAAC;OACnB,EAAE0E,MAAM;UACL,EAAEE,WAAW;MACjB,EAAED,OAAOE,IAAI,GAAGF,OAAOG,IAAI,IAAI,YAAY,QAAQ;AACzD,CAAC;oBAES,OAAO;wBACL9E;wBACAkD,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACEqB,OAAO;YACPC,SAAS;gBACPC,SAAS;oBACP,MAAM3D,mBAAmBtB,kBAAkB;oBAC3C,MAAMuB,aAAa,MAAMnB;oBACzB,MAAMI,SAAS,CAAC;;UAEhB,EAAEV,QAAQ6B,QAAQ,CAACC,IAAI,CAAC;SACzB,EAAEjB,iBAAiB,OAAO;UACzB,EAAEA,iBAAiB,QAAQ;UAC3B,EAAEA,iBAAiB,QAAQ;;UAE3B,EAAEW,iBAAiB;wBACL,EAAEtB,kBAAkB,sBAAsB;WACvD,EAAEA,kBAAkB,SAAS;eACzB,EAAEA,kBAAkB,aAAa;gBAChC,EAAEA,kBAAkB,cAAc;;YAEtC,EAAEuB,WAAWf,MAAM,CAAC;;AAEhC,CAAC;oBACS,OAAO;wBACLA;wBACAkD,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACEqB,OAAO;YACPC,SAAS;gBACPC,SAAS;wBACQnF;oBAAf,MAAMyF,UAASzF,kBAAAA,QAAQyF,MAAM,qBAAdzF,gBAAgB0F,SAAS;oBAExC,IAAI,CAACD,QAAQ;wBACX,OAAO;4BACL9B,UAAU;4BACVC,QAAQ;wBACV;oBACF;oBAEA,MAAM,EAAE+B,MAAM,EAAEC,cAAc,EAAEC,aAAa,EAAE,GAC7CJ;oBACF,mEAAmE;oBAC5DE,+BAAAA,OAAQ1F,GAAG;oBACX0F,+BAAAA,OAAQG,WAAW;oBACnBH,+BAAAA,OAAQI,IAAI;oBACZJ,+BAAAA,OAAQK,IAAI;oBACZL,+BAAAA,OAAQM,iBAAiB;oBAEhC,MAAMC,gBAAgB;wBACpBP;wBACAC;wBACAC;oBACF;oBAEA,OAAO;wBACLnF,QAAQyF,KAAKC,SAAS,CAACF,eAAe,MAAM;wBAC5CtC,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACEqB,OAAO;YACPC,SAAS;gBACPC,SAAS;wBAyBSkB;oBAxBhB,MAAM3F,SAAS,EAAE;oBAEjB,gDAAgD;oBAChD,IAAI;4BAIAe;wBAHF,IAAIA,aAAa,MAAMnB;wBACvB,MAAM,EAAEgG,YAAY,EAAE,GAAGlG,QAAQ;wBACjC,MAAMmG,WAAW,MAAMD,cACrB7E,2BAAAA,WAAWd,YAAY,qBAAvBc,yBAAyBb,aAAa;wBAExC,qEAAqE;wBACrE,MAAM4F,SAASD,SAASE,eAAe;wBAEvC,uEAAuE;wBACvE,OAAO;4BACL/F,QAAQ,CAAC,oCAAoC,EAAE8F,OAAO,CAAC;4BACvD5C,QAAQ;wBACV;oBACF,EAAE,OAAOnB,GAAG;wBACV/B,OAAO+C,IAAI,CAAC,CAAC,uBAAuB,EAAE,AAAChB,EAAYC,OAAO,CAAC,CAAC;oBAC9D;oBAEA,MAAM,EACJ2D,mBAAmB,EACpB,GAAGjG,QAAQ;oBACZ,MAAM6C,WAAUoD,uCAAAA,mBAAmB,CAACtD,gBAAgB,qBAApCsD,oCAAsC,CAAC3E,WAAE,CAACE,IAAI,GAAG;oBAEjE,IAAI,CAACqB,WAAWA,QAAQS,MAAM,KAAK,GAAG;wBACpC,OAAO;4BACLC,UAAU,CAAC,4BAA4B,EAAEZ,gBAAgB,GAAG,EAAErB,WAAE,CAACE,IAAI,GAAG,CAAC;4BACzEgC,QAAQ;wBACV;oBACF;oBAEA,qGAAqG;oBACrG,MAAM8C,OAAOtG,QAAQ;oBACrB,IAAIuG;oBACJ,IAAI;wBACF,MAAMC,WAAWF,KAAKG,OAAO,CAACzG,QAAQ8D,OAAO,CAAC;wBAC9CyC,4BAA4BD,KAAK7B,IAAI,CAAC+B,UAAU;oBAClD,EAAE,OAAOnE,GAAG;oBACV,mGAAmG;oBACrG;oBAEA,MAAMqE,aAAa,CAACC;wBAClB,IAAI;4BACF,MAAM9C,WAAW7D,QAAQ8D,OAAO,CAAC6C;4BACjC,MAAMC,aAAajC,GAAGkC,UAAU,CAAChD;4BACjC,IAAIiD;4BACJ,IAAIC;4BAEJ,IAAI;gCACFA,cAAc,CAAC,CAAC/G,QAAQ6D,UAAUwC,eAAe;4BACnD,EAAE,OAAOhE,GAAG;gCACVyE,YAAY,AAACzE,EAAYC,OAAO;4BAClC;4BAEAhC,OAAO+C,IAAI,CACT,CAAC,EAAEsD,QAAQ,SAAS,EAAEC,WAAW,gBAAgB,EAAEG,YAAY,CAAC;4BAElE,IAAID,WAAW;gCACbxG,OAAO+C,IAAI,CAAC,CAAC,EAAEsD,QAAQ,cAAc,EAAEG,aAAa,UAAU,CAAC;4BACjE;4BAEA,IAAIC,aAAa;gCACf,OAAO;4BACT;wBACF,EAAE,OAAO1E,GAAG;4BACV/B,OAAO+C,IAAI,CACT,CAAC,EAAEsD,QAAQ,iBAAiB,EAC1B,AAACtE,EAAYC,OAAO,IAAI,UACzB,CAAC;wBAEN;wBACA,OAAO;oBACT;oBAEA,KAAK,MAAMoB,UAAUb,QAAS;wBAC5B,MAAMc,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,CAAC,CAAC;wBAC3D,yFAAyF;wBACzF,mHAAmH;wBACnH,IAAI8C,WAAW/C,gBAAgB;4BAC7B;wBACF;wBAEA,4CAA4C;wBAC5C,IAAI,CAAC4C,2BAA2B;4BAC9B;wBACF;wBAEAG,WAAWJ,KAAK7B,IAAI,CAAC8B,2BAA2B5C;oBAClD;oBAEA,OAAO;wBACLrD,QAAQA,OAAOmE,IAAI,CAAC;wBACpBjB,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACE,oFAAoF;YACpF,+EAA+E;YAC/E,mEAAmE;YACnEqB,OAAO;YACPC,SAAS;gBACPkC,OAAO;oBACL,MAAMvE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAS;4BACrBa,MAAM;gCAAC;6BAAY;wBACrB;qBACD,EACDvB;gBAEJ;gBACAwE,OAAO;oBACL,MAAMxE,cAAc,CAAC;;;;;UAKrB,CAAC;oBAED,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAW;4BACvBa,MAAM;gCAAC;6BAAW;wBACpB;qBACD,EACDvB;gBAEJ;gBACAyE,QAAQ;oBACN,MAAMzE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAY;4BACxBa,MAAM;gCAAC;6BAAK;wBACd;wBACA;4BACEd,KAAK;4BACLC,WAAW,EAAE;4BACba,MAAM,EAAE;wBACV;qBACD,EACDvB;gBAEJ;YACF;QACF;KACD;IAED,4CAA4C;IAC5C,MAAM4C,SAGD,EAAE;IAEPrE,QAAQC,GAAG,CAAC;IACZ,KAAK,MAAMkG,QAAQvC,MAAO;QACxB,IAAIuC,KAAKC,cAAc,IAAID,KAAKC,cAAc,KAAKzE,iBAAiB;YAClE0C,OAAOhC,IAAI,CAAC;gBACVwB,OAAOsC,KAAKtC,KAAK;gBACjBrB,QAAQ;oBACND,UAAU8D;oBACV/G,QAAQ,CAAC,UAAU,EAAEgB,WAAE,CAACC,QAAQ,GAAG,GAAG,EAAE4F,KAAKC,cAAc,CAAC,GAAG,EAC7DD,KAAKtC,KAAK,CACX,CAAC;oBACFrB,QAAQ;gBACV;YACF;YACA;QACF;QAEA,MAAM8D,aAAaH,KAAKrC,OAAO,CAACnC,gBAAgB,IAAIwE,KAAKrC,OAAO,CAACC,OAAO;QACxE,IAAIwC;QACJ,IAAI;YACFA,aAAa,MAAMD;QACrB,EAAE,OAAOjF,GAAG;YACVkF,aAAa;gBACXhE,UAAU,CAAC,8CAA8C,EACvD,AAAClB,EAAYC,OAAO,CACrB,CAAC;gBACFkB,QAAQ;YACV;QACF;QAEAxC,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEkG,KAAKtC,KAAK,CAAC,EAAE,EAAE0C,WAAW/D,MAAM,CAAC,CAAC;QACnD,IAAI+D,WAAWhE,QAAQ,EAAE;YACvBvC,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEsG,WAAWhE,QAAQ,CAAC,CAAC;QACxC;QAEA8B,OAAOhC,IAAI,CAAC;YACVwB,OAAOsC,KAAKtC,KAAK;YACjBrB,QAAQ+D;QACV;IACF;IAEAvG,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEmB,IAAAA,gBAAI,EAAC,gCAAgC,CAAC;IAEvDpB,QAAQC,GAAG,CAAC,CAAC,wDAAwD,CAAC;IACtE,KAAK,MAAM,EAAE4D,KAAK,EAAErB,MAAM,EAAE,IAAI6B,OAAQ;QACtCrE,QAAQC,GAAG,CAAC,CAAC,MAAM,EAAE4D,MAAM,CAAC;QAE5B,IAAIrB,OAAOD,QAAQ,EAAE;YACnBvC,QAAQC,GAAG,CAACuC,OAAOD,QAAQ;QAC7B;QAEA,IAAIC,OAAOlD,MAAM,EAAE;YACjBU,QAAQC,GAAG,CAACuC,OAAOlD,MAAM;QAC3B;IACF;AACF;AAEA;;;;CAIC,GACD,MAAMZ,WAAuB,OAAOsE;IAClC,IAAIA,IAAI,CAAC,SAAS,EAAE;QAClBjD;QACA;IACF;IAEA,IAAI,CAACiD,IAAI,CAAC,YAAY,EAAE;QACtB,MAAM7C;IACR,OAAO;QACL,MAAMuD;IACR;AACF"}