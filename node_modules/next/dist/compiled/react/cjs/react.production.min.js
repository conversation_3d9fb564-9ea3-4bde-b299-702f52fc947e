/*
 React
 react.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var l=Symbol.for("react.element"),n=Symbol.for("react.portal"),p=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),r=Symbol.for("react.profiler"),t=Symbol.for("react.provider"),u=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),x=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),z=Symbol.iterator;function A(a){if(null===a||"object"!==typeof a)return null;a=z&&a[z]||a["@@iterator"];return"function"===typeof a?a:null}
var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,D={};function E(a,b,c){this.props=a;this.context=b;this.refs=D;this.updater=c||B}E.prototype.isReactComponent={};
E.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,a,b,"setState")};E.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};function F(){}F.prototype=E.prototype;function G(a,b,c){this.props=a;this.context=b;this.refs=D;this.updater=c||B}var H=G.prototype=new F;
H.constructor=G;C(H,E.prototype);H.isPureReactComponent=!0;var I=Array.isArray,J=Object.prototype.hasOwnProperty,K={current:null},L={key:!0,ref:!0,__self:!0,__source:!0};
function M(a,b,c){var f,d={},e=null,g=null;if(null!=b)for(f in void 0!==b.ref&&(g=b.ref),void 0!==b.key&&(e=""+b.key),b)J.call(b,f)&&!L.hasOwnProperty(f)&&(d[f]=b[f]);var h=arguments.length-2;if(1===h)d.children=c;else if(1<h){for(var k=Array(h),m=0;m<h;m++)k[m]=arguments[m+2];d.children=k}if(a&&a.defaultProps)for(f in h=a.defaultProps,h)void 0===d[f]&&(d[f]=h[f]);return{$$typeof:l,type:a,key:e,ref:g,props:d,_owner:K.current}}
function N(a,b){return{$$typeof:l,type:a.type,key:b,ref:a.ref,props:a.props,_owner:a._owner}}function O(a){return"object"===typeof a&&null!==a&&a.$$typeof===l}function escape(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(c){return b[c]})}var P=/\/+/g;function Q(a,b){return"object"===typeof a&&null!==a&&null!=a.key?escape(""+a.key):b.toString(36)}
function R(a,b,c,f,d){var e=typeof a;if("undefined"===e||"boolean"===e)a=null;var g=!1;if(null===a)g=!0;else switch(e){case "string":case "number":g=!0;break;case "object":switch(a.$$typeof){case l:case n:g=!0}}if(g)return g=a,d=d(g),a=""===f?"."+Q(g,0):f,I(d)?(c="",null!=a&&(c=a.replace(P,"$&/")+"/"),R(d,b,c,"",function(m){return m})):null!=d&&(O(d)&&(d=N(d,c+(!d.key||g&&g.key===d.key?"":(""+d.key).replace(P,"$&/")+"/")+a)),b.push(d)),1;g=0;f=""===f?".":f+":";if(I(a))for(var h=0;h<a.length;h++){e=
a[h];var k=f+Q(e,h);g+=R(e,b,c,k,d)}else if(k=A(a),"function"===typeof k)for(a=k.call(a),h=0;!(e=a.next()).done;)e=e.value,k=f+Q(e,h++),g+=R(e,b,c,k,d);else if("object"===e)throw b=String(a),Error("Objects are not valid as a React child (found: "+("[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b)+"). If you meant to render a collection of children, use an array instead.");return g}
function S(a,b,c){if(null==a)return a;var f=[],d=0;R(a,f,"","",function(e){return b.call(c,e,d++)});return f}function T(a){if(-1===a._status){var b=a._result;b=b();b.then(function(c){if(0===a._status||-1===a._status)a._status=1,a._result=c},function(c){if(0===a._status||-1===a._status)a._status=2,a._result=c});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result.default;throw a._result;}var U={current:null};function V(){return new WeakMap}
function W(){return{s:0,v:void 0,o:null,p:null}}var X={current:null},Y={transition:null},Z={ReactCurrentDispatcher:X,ReactCurrentCache:U,ReactCurrentBatchConfig:Y,ReactCurrentOwner:K};
exports.Children={map:S,forEach:function(a,b,c){S(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;S(a,function(){b++});return b},toArray:function(a){return S(a,function(b){return b})||[]},only:function(a){if(!O(a))throw Error("React.Children.only expected to receive a single React element child.");return a}};exports.Component=E;exports.Fragment=p;exports.Profiler=r;exports.PureComponent=G;exports.StrictMode=q;exports.Suspense=w;
exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Z;
exports.cache=function(a){return function(){var b=U.current;if(!b)return a.apply(null,arguments);var c=b.getCacheForType(V);b=c.get(a);void 0===b&&(b=W(),c.set(a,b));c=0;for(var f=arguments.length;c<f;c++){var d=arguments[c];if("function"===typeof d||"object"===typeof d&&null!==d){var e=b.o;null===e&&(b.o=e=new WeakMap);b=e.get(d);void 0===b&&(b=W(),e.set(d,b))}else e=b.p,null===e&&(b.p=e=new Map),b=e.get(d),void 0===b&&(b=W(),e.set(d,b))}if(1===b.s)return b.v;if(2===b.s)throw b.v;try{var g=a.apply(null,
arguments);c=b;c.s=1;return c.v=g}catch(h){throw g=b,g.s=2,g.v=h,h;}}};
exports.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+a+".");var f=C({},a.props),d=a.key,e=a.ref,g=a._owner;if(null!=b){void 0!==b.ref&&(e=b.ref,g=K.current);void 0!==b.key&&(d=""+b.key);if(a.type&&a.type.defaultProps)var h=a.type.defaultProps;for(k in b)J.call(b,k)&&!L.hasOwnProperty(k)&&(f[k]=void 0===b[k]&&void 0!==h?h[k]:b[k])}var k=arguments.length-2;if(1===k)f.children=c;else if(1<k){h=Array(k);
for(var m=0;m<k;m++)h[m]=arguments[m+2];f.children=h}return{$$typeof:l,type:a.type,key:d,ref:e,props:f,_owner:g}};exports.createContext=function(a){a={$$typeof:u,_currentValue:a,_currentValue2:a,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null};a.Provider={$$typeof:t,_context:a};return a.Consumer=a};exports.createElement=M;exports.createFactory=function(a){var b=M.bind(null,a);b.type=a;return b};exports.createRef=function(){return{current:null}};
exports.forwardRef=function(a){return{$$typeof:v,render:a}};exports.isValidElement=O;exports.lazy=function(a){return{$$typeof:y,_payload:{_status:-1,_result:a},_init:T}};exports.memo=function(a,b){return{$$typeof:x,type:a,compare:void 0===b?null:b}};exports.startTransition=function(a){var b=Y.transition;Y.transition={};try{a()}finally{Y.transition=b}};exports.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.");};exports.unstable_useCacheRefresh=function(){return X.current.useCacheRefresh()};
exports.use=function(a){return X.current.use(a)};exports.useCallback=function(a,b){return X.current.useCallback(a,b)};exports.useContext=function(a){return X.current.useContext(a)};exports.useDebugValue=function(){};exports.useDeferredValue=function(a,b){return X.current.useDeferredValue(a,b)};exports.useEffect=function(a,b){return X.current.useEffect(a,b)};exports.useId=function(){return X.current.useId()};exports.useImperativeHandle=function(a,b,c){return X.current.useImperativeHandle(a,b,c)};
exports.useInsertionEffect=function(a,b){return X.current.useInsertionEffect(a,b)};exports.useLayoutEffect=function(a,b){return X.current.useLayoutEffect(a,b)};exports.useMemo=function(a,b){return X.current.useMemo(a,b)};exports.useOptimistic=function(a,b){return X.current.useOptimistic(a,b)};exports.useReducer=function(a,b,c){return X.current.useReducer(a,b,c)};exports.useRef=function(a){return X.current.useRef(a)};exports.useState=function(a){return X.current.useState(a)};
exports.useSyncExternalStore=function(a,b,c){return X.current.useSyncExternalStore(a,b,c)};exports.useTransition=function(){return X.current.useTransition()};exports.version="18.3.0-canary-4b84f1161-20240318";

//# sourceMappingURL=react.production.min.js.map
