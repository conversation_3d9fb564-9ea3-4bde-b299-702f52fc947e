/*
 React
 react.shared-subset.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var m=Object.assign,n={current:null};function p(){return new Map}
if("function"===typeof fetch){var q=fetch,r=function(a,b){var d=n.current;if(!d||b&&b.signal&&b.signal!==d.getCacheSignal())return q(a,b);if("string"!==typeof a||b){var c="string"===typeof a||a instanceof URL?new Request(a,b):a;if("GET"!==c.method&&"HEAD"!==c.method||c.keepalive)return q(a,b);var e=JSON.stringify([c.method,Array.from(c.headers.entries()),c.mode,c.redirect,c.credentials,c.referrer,c.referrerPolicy,c.integrity]);c=c.url}else e='["GET",[],null,"follow",null,null,null,null]',c=a;var f=
d.getCacheForType(p);d=f.get(c);if(void 0===d)a=q(a,b),f.set(c,[e,a]);else{c=0;for(f=d.length;c<f;c+=2){var h=d[c+1];if(d[c]===e)return a=h,a.then(function(g){return g.clone()})}a=q(a,b);d.push(e,a)}return a.then(function(g){return g.clone()})};m(r,q);try{fetch=r}catch(a){try{globalThis.fetch=r}catch(b){console.warn("React was unable to patch the fetch() function in this environment. Suspensey APIs might not work correctly as a result.")}}}
var t={current:null},u={current:null},v={ReactCurrentDispatcher:t,ReactCurrentOwner:u},w={ReactCurrentCache:n},x=Symbol.for("react.element"),y=Symbol.for("react.portal"),z=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),C=Symbol.for("react.forward_ref"),D=Symbol.for("react.suspense"),E=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),G=Symbol.iterator;
function H(a){if(null===a||"object"!==typeof a)return null;a=G&&a[G]||a["@@iterator"];return"function"===typeof a?a:null}function I(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,d=1;d<arguments.length;d++)b+="&args[]="+encodeURIComponent(arguments[d]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
var J={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},K={};function L(a,b,d){this.props=a;this.context=b;this.refs=K;this.updater=d||J}L.prototype.isReactComponent={};L.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error(I(85));this.updater.enqueueSetState(this,a,b,"setState")};L.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};
function M(){}M.prototype=L.prototype;function N(a,b,d){this.props=a;this.context=b;this.refs=K;this.updater=d||J}var O=N.prototype=new M;O.constructor=N;m(O,L.prototype);O.isPureReactComponent=!0;var P=Array.isArray,Q=Object.prototype.hasOwnProperty,R={key:!0,ref:!0,__self:!0,__source:!0};function S(a,b){return{$$typeof:x,type:a.type,key:b,ref:a.ref,props:a.props,_owner:a._owner}}function T(a){return"object"===typeof a&&null!==a&&a.$$typeof===x}
function escape(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(d){return b[d]})}var U=/\/+/g;function V(a,b){return"object"===typeof a&&null!==a&&null!=a.key?escape(""+a.key):b.toString(36)}
function W(a,b,d,c,e){var f=typeof a;if("undefined"===f||"boolean"===f)a=null;var h=!1;if(null===a)h=!0;else switch(f){case "string":case "number":h=!0;break;case "object":switch(a.$$typeof){case x:case y:h=!0}}if(h)return h=a,e=e(h),a=""===c?"."+V(h,0):c,P(e)?(d="",null!=a&&(d=a.replace(U,"$&/")+"/"),W(e,b,d,"",function(l){return l})):null!=e&&(T(e)&&(e=S(e,d+(!e.key||h&&h.key===e.key?"":(""+e.key).replace(U,"$&/")+"/")+a)),b.push(e)),1;h=0;c=""===c?".":c+":";if(P(a))for(var g=0;g<a.length;g++){f=
a[g];var k=c+V(f,g);h+=W(f,b,d,k,e)}else if(k=H(a),"function"===typeof k)for(a=k.call(a),g=0;!(f=a.next()).done;)f=f.value,k=c+V(f,g++),h+=W(f,b,d,k,e);else if("object"===f)throw b=String(a),Error(I(31,"[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b));return h}function X(a,b,d){if(null==a)return a;var c=[],e=0;W(a,c,"","",function(f){return b.call(d,f,e++)});return c}
function Y(a){if(-1===a._status){var b=a._result;b=b();b.then(function(d){if(0===a._status||-1===a._status)a._status=1,a._result=d},function(d){if(0===a._status||-1===a._status)a._status=2,a._result=d});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result.default;throw a._result;}function aa(){return new WeakMap}function Z(){return{s:0,v:void 0,o:null,p:null}}
exports.Children={map:X,forEach:function(a,b,d){X(a,function(){b.apply(this,arguments)},d)},count:function(a){var b=0;X(a,function(){b++});return b},toArray:function(a){return X(a,function(b){return b})||[]},only:function(a){if(!T(a))throw Error(I(143));return a}};exports.Fragment=z;exports.Profiler=B;exports.StrictMode=A;exports.Suspense=D;exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=v;exports.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=w;
exports.cache=function(a){return function(){var b=n.current;if(!b)return a.apply(null,arguments);var d=b.getCacheForType(aa);b=d.get(a);void 0===b&&(b=Z(),d.set(a,b));d=0;for(var c=arguments.length;d<c;d++){var e=arguments[d];if("function"===typeof e||"object"===typeof e&&null!==e){var f=b.o;null===f&&(b.o=f=new WeakMap);b=f.get(e);void 0===b&&(b=Z(),f.set(e,b))}else f=b.p,null===f&&(b.p=f=new Map),b=f.get(e),void 0===b&&(b=Z(),f.set(e,b))}if(1===b.s)return b.v;if(2===b.s)throw b.v;try{var h=a.apply(null,
arguments);d=b;d.s=1;return d.v=h}catch(g){throw h=b,h.s=2,h.v=g,g;}}};
exports.cloneElement=function(a,b,d){if(null===a||void 0===a)throw Error(I(267,a));var c=m({},a.props),e=a.key,f=a.ref,h=a._owner;if(null!=b){void 0!==b.ref&&(f=b.ref,h=u.current);void 0!==b.key&&(e=""+b.key);if(a.type&&a.type.defaultProps)var g=a.type.defaultProps;for(k in b)Q.call(b,k)&&!R.hasOwnProperty(k)&&(c[k]=void 0===b[k]&&void 0!==g?g[k]:b[k])}var k=arguments.length-2;if(1===k)c.children=d;else if(1<k){g=Array(k);for(var l=0;l<k;l++)g[l]=arguments[l+2];c.children=g}return{$$typeof:x,type:a.type,
key:e,ref:f,props:c,_owner:h}};exports.createElement=function(a,b,d){var c,e={},f=null,h=null;if(null!=b)for(c in void 0!==b.ref&&(h=b.ref),void 0!==b.key&&(f=""+b.key),b)Q.call(b,c)&&!R.hasOwnProperty(c)&&(e[c]=b[c]);var g=arguments.length-2;if(1===g)e.children=d;else if(1<g){for(var k=Array(g),l=0;l<g;l++)k[l]=arguments[l+2];e.children=k}if(a&&a.defaultProps)for(c in g=a.defaultProps,g)void 0===e[c]&&(e[c]=g[c]);return{$$typeof:x,type:a,key:f,ref:h,props:e,_owner:u.current}};exports.createRef=function(){return{current:null}};
exports.createServerContext=function(){throw Error(I(248));};exports.forwardRef=function(a){return{$$typeof:C,render:a}};exports.isValidElement=T;exports.lazy=function(a){return{$$typeof:F,_payload:{_status:-1,_result:a},_init:Y}};exports.memo=function(a,b){return{$$typeof:E,type:a,compare:void 0===b?null:b}};exports.startTransition=function(a){a()};exports.use=function(a){return t.current.use(a)};exports.useCallback=function(a,b){return t.current.useCallback(a,b)};exports.useContext=function(a){return t.current.useContext(a)};
exports.useDebugValue=function(){};exports.useId=function(){return t.current.useId()};exports.useMemo=function(a,b){return t.current.useMemo(a,b)};exports.version="18.3.0-canary-4b84f1161-20240318";

//# sourceMappingURL=react.shared-subset.production.min.js.map
