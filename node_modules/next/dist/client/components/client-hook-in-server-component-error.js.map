{"version": 3, "sources": ["../../../src/client/components/client-hook-in-server-component-error.ts"], "names": ["clientHookInServerComponentError", "<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "React", "useState", "Error"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;;gEAFE;AAEX,SAASA,iCACdC,QAAgB;IAEhB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,uDAAuD;QACvD,IAAI,CAACC,cAAK,CAACC,QAAQ,EAAE;YACnB,MAAM,IAAIC,MACR,AAAC,KAAEN,WAAS;QAEhB;IACF;AACF"}