{"version": 3, "sources": ["../../src/client/next-dev-turbopack.ts"], "names": ["window", "next", "version", "router", "emitter", "self", "__next_set_public_path__", "__webpack_hash__", "devClient", "initHMR", "initialize", "then", "assetPrefix", "__turbopack_load_page_chunks__", "page", "chunksData", "chunkPromises", "map", "__turbopack_load__", "Promise", "all", "catch", "err", "console", "error", "connect", "addMessageListener", "cb", "msg", "type", "startsWith", "sendMessage", "pageBootrap"], "mappings": "AAAA,kCAAkC;;;;;;kBACmB;8EACjC;QAEb;+BACqB;2BACoB;6BAExB;AAGxBA,OAAOC,IAAI,GAAG;IACZC,SAAS,AAAC,KAAEA,SAAO,GAAC;IACpB,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA,QAAM;IACf;IACAC,SAAAA,SAAO;AACT;AACEC,KAAaC,wBAAwB,GAAG,KAAO;AAC/CD,KAAaE,gBAAgB,GAAG;AAKlC,MAAMC,YAAYC,IAAAA,4BAAO,EAAC;AAC1BC,IAAAA,YAAU,EAAC;IACTF;AACF,GACGG,IAAI,CAAC;QAAC,EAAEC,WAAW,EAAE;IAElBP,KAAaQ,8BAA8B,GAAG,CAC9CC,MACAC;QAEA,MAAMC,gBAAgBD,WAAWE,GAAG,CAACC;QAErCC,QAAQC,GAAG,CAACJ,eAAeK,KAAK,CAAC,CAACC,MAChCC,QAAQC,KAAK,CAAC,oCAAoCV,MAAMQ;IAE5D;IAEAG,IAAAA,oBAAO,EAAC;QACNC,oBAAmBC,EAAmC;YACpDD,IAAAA,6BAAkB,EAAC,CAACE;oBAKdA;gBAJJ,IAAI,CAAE,CAAA,UAAUA,GAAE,GAAI;oBACpB;gBACF;gBACA,gEAAgE;gBAChE,KAAIA,YAAAA,IAAIC,IAAI,qBAARD,UAAUE,UAAU,CAAC,eAAe;oBACtCH,GAAGC;gBACL;YACF;QACF;QACAG,aAAAA,sBAAW;IACb;IAEA,OAAOC,IAAAA,0BAAW,EAACpB;AACrB,GACCS,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC"}