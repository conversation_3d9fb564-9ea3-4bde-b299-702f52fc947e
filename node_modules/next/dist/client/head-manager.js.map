{"version": 3, "sources": ["../../src/client/head-manager.ts"], "names": ["DOMAttributeNames", "isEqualNode", "initHeadManager", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "reactElementToDOM", "type", "props", "el", "document", "createElement", "p", "hasOwnProperty", "undefined", "attr", "toLowerCase", "setAttribute", "children", "dangerouslySetInnerHTML", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "oldTag", "newTag", "HTMLElement", "nonce", "getAttribute", "cloneTag", "cloneNode", "updateElements", "process", "env", "__NEXT_STRICT_NEXT_HEAD", "components", "headEl", "querySelector", "headMetaTags", "querySelectorAll", "oldTags", "metaCharset", "push", "i", "length", "headTag", "metaTag", "nextS<PERSON>ling", "tagName", "newTags", "map", "filter", "k", "len", "splice", "for<PERSON>ach", "t", "previousSibling", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "meta", "name", "content", "append<PERSON><PERSON><PERSON>", "getElementsByTagName", "headCountEl", "NODE_ENV", "console", "error", "headCount", "Number", "j", "previousElementSibling", "insertBefore", "toString", "mountedInstances", "Set", "updateHead", "head", "tags", "h", "href", "titleComponent", "title"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,iBAAiB;eAAjBA;;IAwDGC,WAAW;eAAXA;;IA2HhB,OAgDC;eAhDuBC;;;AAnLjB,MAAMF,oBAA4C;IACvDG,eAAe;IACfC,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,UAAU;AACZ;AAEA,SAASC,kBAAkB,KAA4B;IAA5B,IAAA,EAAEC,IAAI,EAAEC,KAAK,EAAe,GAA5B;IACzB,MAAMC,KAAkBC,SAASC,aAAa,CAACJ;IAC/C,IAAK,MAAMK,KAAKJ,MAAO;QACrB,IAAI,CAACA,MAAMK,cAAc,CAACD,IAAI;QAC9B,IAAIA,MAAM,cAAcA,MAAM,2BAA2B;QAEzD,6CAA6C;QAC7C,IAAIJ,KAAK,CAACI,EAAE,KAAKE,WAAW;QAE5B,MAAMC,OAAOjB,iBAAiB,CAACc,EAAE,IAAIA,EAAEI,WAAW;QAClD,IACET,SAAS,YACRQ,CAAAA,SAAS,WAAWA,SAAS,WAAWA,SAAS,UAAS,GAC3D;YACEN,EAAwB,CAACM,KAAK,GAAG,CAAC,CAACP,KAAK,CAACI,EAAE;QAC/C,OAAO;YACLH,GAAGQ,YAAY,CAACF,MAAMP,KAAK,CAACI,EAAE;QAChC;IACF;IAEA,MAAM,EAAEM,QAAQ,EAAEC,uBAAuB,EAAE,GAAGX;IAC9C,IAAIW,yBAAyB;QAC3BV,GAAGW,SAAS,GAAGD,wBAAwBE,MAAM,IAAI;IACnD,OAAO,IAAIH,UAAU;QACnBT,GAAGa,WAAW,GACZ,OAAOJ,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACdA,SAASO,IAAI,CAAC,MACd;IACR;IACA,OAAOhB;AACT;AAgBO,SAASV,YAAY2B,MAAe,EAAEC,MAAe;IAC1D,IAAID,kBAAkBE,eAAeD,kBAAkBC,aAAa;QAClE,MAAMC,QAAQF,OAAOG,YAAY,CAAC;QAClC,8FAA8F;QAC9F,4FAA4F;QAC5F,IAAID,SAAS,CAACH,OAAOI,YAAY,CAAC,UAAU;YAC1C,MAAMC,WAAWJ,OAAOK,SAAS,CAAC;YAClCD,SAASd,YAAY,CAAC,SAAS;YAC/Bc,SAASF,KAAK,GAAGA;YACjB,OAAOA,UAAUH,OAAOG,KAAK,IAAIH,OAAO3B,WAAW,CAACgC;QACtD;IACF;IAEA,OAAOL,OAAO3B,WAAW,CAAC4B;AAC5B;AAEA,IAAIM;AAEJ,IAAIC,QAAQC,GAAG,CAACC,uBAAuB,EAAE;IACvCH,iBAAiB,CAAC1B,MAAc8B;QAC9B,MAAMC,SAAS5B,SAAS6B,aAAa,CAAC;QACtC,IAAI,CAACD,QAAQ;QAEb,MAAME,eAAeF,OAAOG,gBAAgB,CAAC,6BAA6B,EAAE;QAC5E,MAAMC,UAAqB,EAAE;QAE7B,IAAInC,SAAS,QAAQ;YACnB,MAAMoC,cAAcL,OAAOC,aAAa,CAAC;YACzC,IAAII,aAAa;gBACfD,QAAQE,IAAI,CAACD;YACf;QACF;QAEA,IAAK,IAAIE,IAAI,GAAGA,IAAIL,aAAaM,MAAM,EAAED,IAAK;gBAIxCE;YAHJ,MAAMC,UAAUR,YAAY,CAACK,EAAE;YAC/B,MAAME,UAAUC,QAAQC,WAAW;YAEnC,IAAIF,CAAAA,4BAAAA,mBAAAA,QAASG,OAAO,qBAAhBH,iBAAkB/B,WAAW,QAAOT,MAAM;gBAC5CmC,QAAQE,IAAI,CAACG;YACf;QACF;QACA,MAAMI,UAAU,AAACd,WAAWe,GAAG,CAAC9C,mBAAqC+C,MAAM,CACzE,CAAC1B;YACC,IAAK,IAAI2B,IAAI,GAAGC,MAAMb,QAAQI,MAAM,EAAEQ,IAAIC,KAAKD,IAAK;gBAClD,MAAM5B,SAASgB,OAAO,CAACY,EAAE;gBACzB,IAAIvD,YAAY2B,QAAQC,SAAS;oBAC/Be,QAAQc,MAAM,CAACF,GAAG;oBAClB,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAGFZ,QAAQe,OAAO,CAAC,CAACC;gBAKfA;YAJA,MAAMV,UAAUU,EAAEC,eAAe;YACjC,IAAIX,WAAWA,QAAQlB,YAAY,CAAC,YAAY,aAAa;oBAC3D4B;iBAAAA,iBAAAA,EAAEE,UAAU,qBAAZF,eAAcG,WAAW,CAACb;YAC5B;aACAU,gBAAAA,EAAEE,UAAU,qBAAZF,cAAcG,WAAW,CAACH;QAC5B;QACAP,QAAQM,OAAO,CAAC,CAACC;gBAMTA;YALN,MAAMI,OAAOpD,SAASC,aAAa,CAAC;YACpCmD,KAAKC,IAAI,GAAG;YACZD,KAAKE,OAAO,GAAG;YAEf,sDAAsD;YACtD,IAAI,CAAEN,CAAAA,EAAAA,aAAAA,EAAER,OAAO,qBAATQ,WAAW1C,WAAW,QAAO,UAAU0C,EAAE5B,YAAY,CAAC,UAAS,GAAI;gBACvEQ,OAAO2B,WAAW,CAACH;YACrB;YACAxB,OAAO2B,WAAW,CAACP;QACrB;IACF;AACF,OAAO;IACLzB,iBAAiB,CAAC1B,MAAc8B;QAC9B,MAAMC,SAAS5B,SAASwD,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACvD,MAAMC,cAA+B7B,OAAOC,aAAa,CACvD;QAEF,IAAIL,QAAQC,GAAG,CAACiC,QAAQ,KAAK,cAAc;YACzC,IAAI,CAACD,aAAa;gBAChBE,QAAQC,KAAK,CACX;gBAEF;YACF;QACF;QAEA,MAAMC,YAAYC,OAAOL,YAAYH,OAAO;QAC5C,MAAMtB,UAAqB,EAAE;QAE7B,IACE,IAAIG,IAAI,GAAG4B,IAAIN,YAAYO,sBAAsB,EACjD7B,IAAI0B,WACJ1B,KAAK4B,IAAIA,CAAAA,qBAAAA,EAAGC,sBAAsB,KAAI,KACtC;gBACID;YAAJ,IAAIA,CAAAA,sBAAAA,aAAAA,EAAGvB,OAAO,qBAAVuB,WAAYzD,WAAW,QAAOT,MAAM;gBACtCmC,QAAQE,IAAI,CAAC6B;YACf;QACF;QACA,MAAMtB,UAAU,AAACd,WAAWe,GAAG,CAAC9C,mBAAqC+C,MAAM,CACzE,CAAC1B;YACC,IAAK,IAAI2B,IAAI,GAAGC,MAAMb,QAAQI,MAAM,EAAEQ,IAAIC,KAAKD,IAAK;gBAClD,MAAM5B,SAASgB,OAAO,CAACY,EAAE;gBACzB,IAAIvD,YAAY2B,QAAQC,SAAS;oBAC/Be,QAAQc,MAAM,CAACF,GAAG;oBAClB,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAGFZ,QAAQe,OAAO,CAAC,CAACC;gBAAMA;oBAAAA,gBAAAA,EAAEE,UAAU,qBAAZF,cAAcG,WAAW,CAACH;;QACjDP,QAAQM,OAAO,CAAC,CAACC,IAAMpB,OAAOqC,YAAY,CAACjB,GAAGS;QAC9CA,YAAYH,OAAO,GAAG,AACpBO,CAAAA,YACA7B,QAAQI,MAAM,GACdK,QAAQL,MAAM,AAAD,EACb8B,QAAQ;IACZ;AACF;AAEe,SAAS5E;IAItB,OAAO;QACL6E,kBAAkB,IAAIC;QACtBC,YAAY,CAACC;YACX,MAAMC,OAAsC,CAAC;YAE7CD,KAAKvB,OAAO,CAAC,CAACyB;gBACZ,IACE,sDAAsD;gBACtD,oEAAoE;gBACpEA,EAAE3E,IAAI,KAAK,UACX2E,EAAE1E,KAAK,CAAC,uBAAuB,EAC/B;oBACA,IACEE,SAAS6B,aAAa,CAAC,AAAC,sBAAmB2C,EAAE1E,KAAK,CAAC,YAAY,GAAC,OAChE;wBACA;oBACF,OAAO;wBACL0E,EAAE1E,KAAK,CAAC2E,IAAI,GAAGD,EAAE1E,KAAK,CAAC,YAAY;wBACnC0E,EAAE1E,KAAK,CAAC,YAAY,GAAGM;oBACzB;gBACF;gBAEA,MAAMuB,aAAa4C,IAAI,CAACC,EAAE3E,IAAI,CAAC,IAAI,EAAE;gBACrC8B,WAAWO,IAAI,CAACsC;gBAChBD,IAAI,CAACC,EAAE3E,IAAI,CAAC,GAAG8B;YACjB;YAEA,MAAM+C,iBAAiBH,KAAKI,KAAK,GAAGJ,KAAKI,KAAK,CAAC,EAAE,GAAG;YACpD,IAAIA,QAAQ;YACZ,IAAID,gBAAgB;gBAClB,MAAM,EAAElE,QAAQ,EAAE,GAAGkE,eAAe5E,KAAK;gBACzC6E,QACE,OAAOnE,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACdA,SAASO,IAAI,CAAC,MACd;YACR;YACA,IAAI4D,UAAU3E,SAAS2E,KAAK,EAAE3E,SAAS2E,KAAK,GAAGA;YAC9C;gBAAC;gBAAQ;gBAAQ;gBAAQ;gBAAS;aAAS,CAAC5B,OAAO,CAAC,CAAClD;gBACpD0B,eAAe1B,MAAM0E,IAAI,CAAC1E,KAAK,IAAI,EAAE;YACvC;QACF;IACF;AACF"}