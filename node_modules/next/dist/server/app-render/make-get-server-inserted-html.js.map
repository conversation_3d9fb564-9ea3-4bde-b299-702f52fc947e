{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "names": ["makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "basePath", "hasPostponed", "flushedErrorMetaTagsUntilIndex", "polyfillsFlushed", "getServerInsertedHTML", "serverCapturedErrors", "errorMetaTags", "length", "error", "isNotFoundError", "push", "meta", "name", "content", "digest", "process", "env", "NODE_ENV", "isRedirectError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "statusCode", "getRedirectStatusCodeFromError", "isPermanent", "RedirectStatusCode", "PermanentRedirect", "httpEquiv", "stream", "renderToReadableStream", "map", "polyfill", "script", "src", "allReady", "streamToString"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;;8DAZE;0BACc;0BAKzB;4BACgC;sCACR;oCACI;+BACL;;;;;;AAEvB,SAASA,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,QAAQ,EACRC,YAAY,EAMb;IACC,IAAIC,iCAAiC;IACrC,2EAA2E;IAC3E,IAAIC,mBAAmBF;IAEvB,OAAO,eAAeG,sBAAsBC,oBAA6B;QACvE,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOJ,iCAAiCG,qBAAqBE,MAAM,CAAE;YACnE,MAAMC,QAAQH,oBAAoB,CAACH,+BAA+B;YAClEA;YAEA,IAAIO,IAAAA,yBAAe,EAACD,QAAQ;gBAC1BF,cAAcI,IAAI,eAChB,qBAACC;oBAAKC,MAAK;oBAASC,SAAQ;mBAAeL,MAAMM,MAAM,GACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,qBAACN;oBAAKC,MAAK;oBAAaC,SAAQ;mBAAgB,gBAC9C;YAER,OAAO,IAAIK,IAAAA,yBAAe,EAACV,QAAQ;gBACjC,MAAMW,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACb,QACxBR;gBAEF,MAAMsB,aAAaC,IAAAA,wCAA8B,EAACf;gBAClD,MAAMgB,cACJF,eAAeG,sCAAkB,CAACC,iBAAiB,GAAG,OAAO;gBAC/D,IAAIP,aAAa;oBACfb,cAAcI,IAAI,eAChB,qBAACC;wBACCgB,WAAU;wBACVd,SAAS,CAAC,EAAEW,cAAc,IAAI,EAAE,KAAK,EAAEL,YAAY,CAAC;uBAC/CX,MAAMM,MAAM;gBAGvB;YACF;QACF;QAEA,MAAMc,SAAS,MAAMC,IAAAA,kCAAsB,gBACzC;;gBAEG,CAAC1B,qBACAL,6BAAAA,UAAWgC,GAAG,CAAC,CAACC;oBACd,qBAAO,qBAACC;wBAA2B,GAAGD,QAAQ;uBAA1BA,SAASE,GAAG;gBAClC;gBACDlC;gBACAO;;;QAIL,6DAA6D;QAC7D,IAAI,CAACH,kBAAkBA,mBAAmB;QAE1C,mCAAmC;QACnC,MAAMyB,OAAOM,QAAQ;QAErB,OAAOC,IAAAA,oCAAc,EAACP;IACxB;AACF"}