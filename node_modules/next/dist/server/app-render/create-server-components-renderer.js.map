{"version": 3, "sources": ["../../../src/server/app-render/create-server-components-renderer.tsx"], "names": ["createReactServerRenderer", "children", "ComponentMod", "clientReferenceManifest", "onError", "onPostpone", "flightStream", "renderToReactServerStream", "renderToReadableStream", "clientModules"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;AAAT,SAASA,0BACdC,QAAyB,EACzBC,YAA2B,EAC3BC,uBAA2E,EAC3EC,OAA8C,EAC9CC,UAAqC;IAErC,IAAIC;IACJ,OAAO,SAASC;QACd,IAAID,cAAc;YAChB,OAAOA;QACT,OAAO;YACLA,eAAeJ,aAAaM,sBAAsB,CAChDP,UACAE,wBAAwBM,aAAa,EACrC;gBACEL;gBACAC;YACF;YAEF,OAAOC;QACT;IACF;AACF"}