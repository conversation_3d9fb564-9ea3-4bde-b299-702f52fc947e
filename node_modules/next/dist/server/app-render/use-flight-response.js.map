{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["useFlightResponse", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "createFlightTransformer", "nonce", "formState", "startScriptTag", "JSON", "stringify", "TransformStream", "start", "controller", "enqueue", "htmlEscapeJsonString", "transform", "chunk", "scripts", "flightResponses", "WeakMap", "writable", "flightStream", "clientReferenceManifest", "response", "get", "createFromReadableStream", "TURBOPACK", "require", "renderStream", "forwardStream", "tee", "res", "ssrManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "set", "pipeFlightDataToInlinedStream", "pipeThrough", "createDecodeTransformStream", "createEncodeTransformStream", "pipeTo", "catch", "err", "console", "error"], "mappings": ";;;;+BAoDgBA;;;eAAAA;;;4BAlDqB;8BAI9B;AAEP,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AAEzC,SAASC,wBACPC,KAAyB,EACzBC,SAAyB;IAEzB,MAAMC,iBAAiBF,QACnB,CAAC,cAAc,EAAEG,KAAKC,SAAS,CAACJ,OAAO,CAAC,CAAC,GACzC;IAEJ,OAAO,IAAIK,gBAAgC;QACzC,oCAAoC;QACpCC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAChB,CAAC,EAAEN,eAAe,uCAAuC,EAAEO,IAAAA,gCAAoB,EAC7EN,KAAKC,SAAS,CAAC;gBAACR;aAAgC,GAChD,qBAAqB,EAAEa,IAAAA,gCAAoB,EAC3CN,KAAKC,SAAS,CAAC;gBAACN;gBAAkCG;aAAU,GAC5D,UAAU,CAAC;QAEjB;QACAS,WAAUC,KAAK,EAAEJ,UAAU;YACzB,MAAMK,UAAU,CAAC,EAAEV,eAAe,mBAAmB,EAAEO,IAAAA,gCAAoB,EACzEN,KAAKC,SAAS,CAAC;gBAACP;gBAA4Bc;aAAM,GAClD,UAAU,CAAC;YAEbJ,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAEA,MAAMC,kBAAkB,IAAIC;AASrB,SAASvB,kBACdwB,QAAoC,EACpCC,YAAwC,EACxCC,uBAAgD,EAChDhB,SAAqB,EACrBD,KAAc;IAEd,MAAMkB,WAAWL,gBAAgBM,GAAG,CAACH;IAErC,IAAIE,UAAU;QACZ,OAAOA;IACT;IAEA,wGAAwG;IACxG,IAAIE;IACJ,uGAAuG;IACvG,IAAI3B,QAAQC,GAAG,CAAC2B,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DE,QAAQ,0CAA0CF,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DE,QAAQ,wCAAwCF,wBAAwB;IAC5E;IAEA,MAAM,CAACG,cAAcC,cAAc,GAAGR,aAAaS,GAAG;IACtD,MAAMC,MAAMN,yBAAyBG,cAAc;QACjDI,aAAa;YACXC,eAAeX,wBAAwBW,aAAa;YACpDC,WAAWrC,gBACPyB,wBAAwBa,oBAAoB,GAC5Cb,wBAAwBc,gBAAgB;QAC9C;QACA/B;IACF;IACAa,gBAAgBmB,GAAG,CAAChB,cAAcU;IAElCO,8BAA8BT,eAAeT,UAAUf,OAAOC;IAE9D,OAAOyB;AACT;AAEA,SAASO,8BACPjB,YAAwC,EACxCD,QAAoC,EACpCf,KAAyB,EACzBC,SAAyB;IAEzBe,aACGkB,WAAW,CAACC,IAAAA,yCAA2B,KACvCD,WAAW,CAACnC,wBAAwBC,OAAOC,YAC3CiC,WAAW,CAACE,IAAAA,yCAA2B,KACvCC,MAAM,CAACtB,UACPuB,KAAK,CAAC,CAACC;QACNC,QAAQC,KAAK,CAAC,kDAAkDF;IAClE;AACJ"}