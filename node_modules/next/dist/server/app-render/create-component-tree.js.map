{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["Postpone", "createComponentTree", "postpone", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "missingSlots", "renderOpts", "nextConfigOutput", "experimental", "staticGenerationStore", "componentMod", "staticGenerationBailout", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "StaticGenerationSearchParamsBailoutProvider", "serverHooks", "DynamicServerError", "pagePath", "getDynamicParamFromSegment", "isPrefetch", "searchParamsProps", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "getLayerAssets", "Template", "templateStyles", "templateScripts", "createComponentStylesAndScripts", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "layoutOrPageMod", "getTracer", "trace", "NextNodeServerSpan", "getLayoutOrPageModule", "hideSpan", "spanName", "attributes", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "forceDynamic", "dynamicShouldError", "link", "forceStatic", "fetchCache", "revalidate", "validateRevalidate", "urlPathname", "defaultRevalidate", "isStaticGeneration", "dynamicUsageDescription", "dynamicUsageErr", "LayoutOrPage", "interopDefault", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "params", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "notFoundComponent", "currentStyles", "childCacheNodeSeedData", "hasLoadingComponentInTree", "ppr", "parsedTree", "PARALLEL_ROUTE_DEFAULT_PATH", "add", "seedData", "styles", "childComponentStyles", "child", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "hasLoading", "Boolean", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "children", "isClientComponent", "isClientReference", "meta", "name", "content", "props", "propsForComponent"], "mappings": ";;;;;;;;;;;;;;;IA4BaA,QAAQ;eAARA;;IAYSC,mBAAmB;eAAnBA;;;;8DAvCgB;iCACJ;8BACI;gCAEP;iCACC;iDAEgB;gCACjB;2CACW;4BACP;sCACS;wBAClB;2BACS;;;;;;AAc5B,MAAMD,WAAW,CAAC,EACvBE,QAAQ,EAGT;IACC,oEAAoE;IACpE,OAAOA,SAAS;AAClB;AAKO,eAAeD,oBAAoB,EACxCE,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EACHC,YAAY,EAcb;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,qBAAqB,EACrBC,cAAc,EACZC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,2CAA2C,EAC3CC,aAAa,EAAEC,kBAAkB,EAAE,EACpC,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,UAAU,EACVC,iBAAiB,EAClB,GAAGjB;IAEJ,MAAM,EAAEkB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEC,IAAAA,gCAAe,EAAChC;IAElB,MAAM,EAAEiC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGP;IAEpE,MAAMQ,+BAA+B,IAAIC,IAAInC;IAC7C,MAAMoC,8BAA8B,IAAID,IAAIlC;IAC5C,MAAMoC,2CAA2C,IAAIF,IACnDjC;IAGF,MAAMoC,cAAcC,IAAAA,8BAAc,EAAC;QACjClC;QACAmB;QACAxB,aAAakC;QACbjC,YAAYmC;QACZlC,yBAAyBmC;IAC3B;IAEA,MAAM,CAACG,UAAUC,gBAAgBC,gBAAgB,GAAGZ,WAChD,MAAMa,IAAAA,gEAA+B,EAAC;QACpCtC;QACAuC,UAAUd,QAAQ,CAAC,EAAE;QACrBe,cAAcf,QAAQ,CAAC,EAAE;QACzB9B,aAAakC;QACbjC,YAAYmC;IACd,KACA;QAACU,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGnB,QAChD,MAAMY,IAAAA,gEAA+B,EAAC;QACpCtC;QACAuC,UAAUb,KAAK,CAAC,EAAE;QAClBc,cAAcd,KAAK,CAAC,EAAE;QACtB/B,aAAakC;QACbjC,YAAYmC;IACd,KACA,EAAE;IAEN,MAAM,CAACe,SAASC,eAAeC,eAAe,GAAGrB,UAC7C,MAAMW,IAAAA,gEAA+B,EAAC;QACpCtC;QACAuC,UAAUZ,OAAO,CAAC,EAAE;QACpBa,cAAcb,OAAO,CAAC,EAAE;QACxBhC,aAAakC;QACbjC,YAAYmC;IACd,KACA,EAAE;IAEN,MAAMkB,WAAW,OAAOzB,WAAW;IACnC,MAAM0B,SAAS,OAAOhC,SAAS;IAC/B,MAAM,CAACiC,gBAAgB,GAAG,MAAMC,IAAAA,iBAAS,IAAGC,KAAK,CAC/CC,6BAAkB,CAACC,qBAAqB,EACxC;QACEC,UAAU,CAAEP,CAAAA,YAAYC,MAAK;QAC7BO,UAAU;QACVC,YAAY;YACV,gBAAgBtC;QAClB;IACF,GACA,IAAMmC,IAAAA,mCAAqB,EAAChE;IAG9B;;GAEC,GACD,MAAMoE,wBAAwBV,YAAY,CAACvD;IAC3C;;GAEC,GACD,MAAMkE,uCACJlE,sBAAsBiE;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGlC,WAC/B,MAAMU,IAAAA,gEAA+B,EAAC;QACpCtC;QACAuC,UAAUX,QAAQ,CAAC,EAAE;QACrBY,cAAcZ,QAAQ,CAAC,EAAE;QACzBjC,aAAakC;QACbjC,YAAYmC;IACd,KACA,EAAE;IAEN,IAAIgC,UAAUZ,mCAAAA,gBAAiBY,OAAO;IAEtC,IAAI5D,qBAAqB,UAAU;QACjC,IAAI,CAAC4D,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC1D,sBAAsB2D,YAAY,GAAG;YACrC3D,sBAAsB4D,kBAAkB,GAAG;YAC3C1D,wBAAwB,CAAC,cAAc,CAAC,EAAE;gBACxCwD;gBACAG,MAAM;YACR;QACF;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvB1D,sBAAsB4D,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtC1D,sBAAsB2D,YAAY,GAAG;YAErC,0DAA0D;YAC1D,IAAI,CAAC3D,sBAAsBjB,QAAQ,EAAE;gBACnC,wEAAwE;gBACxE,0CAA0C;gBAC1CmB,wBAAwB,CAAC,aAAa,CAAC,EAAE;oBAAEwD;gBAAQ;YACrD;QACF,OAAO;YACL1D,sBAAsB4D,kBAAkB,GAAG;YAC3C,IAAIF,YAAY,gBAAgB;gBAC9B1D,sBAAsB8D,WAAW,GAAG;YACtC,OAAO;gBACL9D,sBAAsB8D,WAAW,GAAG;YACtC;QACF;IACF;IAEA,IAAI,QAAOhB,mCAAAA,gBAAiBiB,UAAU,MAAK,UAAU;QACnD/D,sBAAsB+D,UAAU,GAAGjB,mCAAAA,gBAAiBiB,UAAU;IAChE;IAEA,IAAI,QAAOjB,mCAAAA,gBAAiBkB,UAAU,MAAK,aAAa;QACtDC,IAAAA,8BAAkB,EAChBnB,mCAAAA,gBAAiBkB,UAAU,EAC3BhE,sBAAsBkE,WAAW;IAErC;IAEA,IAAI,QAAOpB,mCAAAA,gBAAiBkB,UAAU,MAAK,UAAU;QACnDrE,IAAIwE,iBAAiB,GAAGrB,gBAAgBkB,UAAU;QAElD,IACE,OAAOhE,sBAAsBgE,UAAU,KAAK,eAC3C,OAAOhE,sBAAsBgE,UAAU,KAAK,YAC3ChE,sBAAsBgE,UAAU,GAAGrE,IAAIwE,iBAAiB,EAC1D;YACAnE,sBAAsBgE,UAAU,GAAGrE,IAAIwE,iBAAiB;QAC1D;QAEA,IACE,CAACnE,sBAAsB8D,WAAW,IAClC9D,sBAAsBoE,kBAAkB,IACxCzE,IAAIwE,iBAAiB,KAAK,KAC1B,wEAAwE;QACxE,0CAA0C;QAC1C,CAACnE,sBAAsBjB,QAAQ,EAC/B;YACA,MAAMsF,0BAA0B,CAAC,yBAAyB,EAAEtD,QAAQ,CAAC;YACrEf,sBAAsBqE,uBAAuB,GAAGA;YAEhD,MAAM,IAAI7D,mBAAmB6D;QAC/B;IACF;IAEA,oEAAoE;IACpE,IAAIrE,sBAAsBsE,eAAe,EAAE;QACzC,MAAMtE,sBAAsBsE,eAAe;IAC7C;IAEA,MAAMC,eAAqDzB,kBACvD0B,IAAAA,8BAAc,EAAC1B,mBACf2B;IAEJ;;GAEC,GACD,IAAIC,YAAYH;IAChB,MAAMI,eAAeC,OAAOC,IAAI,CAAC5D;IACjC,MAAM6D,aAAaH,aAAaI,MAAM,GAAG;IAEzC,gGAAgG;IAChG,6EAA6E;IAC7E,4GAA4G;IAC5G,gHAAgH;IAChH,mCAAmC;IACnC,IAAID,cAAcxB,yBAAyBiB,cAAc;QACvDG,YAAY,CAACM;YACX,MAAMC,oBAAoBzB;YAC1B,MAAM0B,sBAAsBX;YAC5B,qBACE,qBAACpE;gBACCoB,UACE0D,kCACE;;wBACGrD;sCAKD,sBAACsD;4BAAoBC,QAAQH,eAAeG,MAAM;;gCAC/C1B;8CACD,qBAACwB;;;;qBAGHR;0BAGN,cAAA,qBAACS;oBAAqB,GAAGF,cAAc;;;QAG7C;IACF;IAEA,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAAC3C,CAAAA,UAAU,OAAO6B,cAAc,WAAU,KAC1C,CAACa,mBAAmBb,YACpB;YACA,MAAM,IAAIe,MACR,CAAC,sDAAsD,EAAEhF,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAO6B,mBAAmB,eAC1B,CAACiD,mBAAmBjD,iBACpB;YACA,MAAM,IAAImD,MACR,CAAC,8DAA8D,EAAE1E,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAO0B,YAAY,eAAe,CAAC8C,mBAAmB9C,UAAU;YAClE,MAAM,IAAIgD,MACR,CAAC,0DAA0D,EAAE1E,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAOyC,aAAa,eAAe,CAAC+B,mBAAmB/B,WAAW;YACpE,MAAM,IAAIiC,MACR,CAAC,2DAA2D,EAAE1E,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAM2E,eAAehF,2BAA2BK;IAChD;;GAEC,GACD,MAAM4E,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGzG,YAAY;QACf,CAACuG,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEAzG;IACN,4BAA4B;IAC5B,MAAM2G,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGhF;IAEhE,EAAE;IACF,8EAA8E;IAC9E,kBAAkB;IAClB,MAAMiF,mBAAmB,MAAMC,QAAQC,GAAG,CACxCtB,OAAOC,IAAI,CAAC5D,gBAAgBkF,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwClH,YAC1C;YAACgH;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgBtF,cAAc,CAACmF,iBAAiB;QAEtD,MAAMI,oBACJhD,YAAY6C,mCAAqB,qBAAC7C,gBAAciB;QAElD,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIgC,gBAAgBhC;QACpB,IAAIiC,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,qEAAqE;QACrE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3B/F,cACC8B,CAAAA,WAAW,CAACkE,IAAAA,oDAAyB,EAACJ,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAACxG,aAAa6G,GAAG,EACjB;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAIxB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB1F,cAAc;gBAC1D,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMiH,aAAa3F,IAAAA,gCAAe,EAACqF;gBACnC,IAAIM,WAAW/F,gBAAgB,KAAKgG,iDAA2B,EAAE;oBAC/DlH,aAAamH,GAAG,CAACX;gBACnB;YACF;YAEA,MAAM,EAAEY,QAAQ,EAAEC,QAAQC,oBAAoB,EAAE,GAC9C,MAAMpI,oBAAoB;gBACxBE,mBAAmB,CAACmI;oBAClB,OAAOnI,kBAAkB;2BAAIsH;2BAAuBa;qBAAM;gBAC5D;gBACAlI,YAAYsH;gBACZpH,cAAcwG;gBACdtG,oBAAoBkE;gBACpBjE,aAAakC;gBACbjC,YAAYmC;gBACZlC,yBAAyBmC;gBACzBlC;gBACAC;gBACAC;gBACAC;YACF;YAEF6G,gBAAgBS;YAChBR,yBAAyBM;QAC3B;QAEA,4CAA4C;QAC5C,OAAO;YACLZ;0BACA,qBAAChG;gBACCgH,mBAAmBhB;gBACnBiB,aAAarI,kBAAkBsH;gBAC/BhF,SAASmB,wBAAU,qBAACA,eAAagC;gBACjC/B,eAAeA;gBACfC,gBAAgBA;gBAChB,sKAAsK;gBACtK2E,YAAYC,QAAQ9E;gBACpBpB,OAAOiB;gBACPC,aAAaA;gBACbC,cAAcA;gBACdpB,wBACE,qBAACU;8BACC,cAAA,qBAACzB;;gBAGL0B,gBAAgBA;gBAChBC,iBAAiBA;gBACjBT,UAAUiF;gBACV/C,gBAAgBA;gBAChBwD,QAAQR;;YAEVC;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIc,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMlB,iBAAiBP,iBAAkB;QAC5C,MAAM,CAACI,kBAAkBsB,mBAAmBC,WAAW,GAAGpB;QAC1DiB,kBAAkB,CAACpB,iBAAiB,GAAGsB;QACvCD,8BAA8B,CAACrB,iBAAiB,GAAGuB;IACrD;IAEA,wIAAwI;IACxI,IAAI,CAACjD,WAAW;QACd,OAAO;YACLsC,UAAU;gBACRlB;gBACA2B;gBACA,wEAAwE;gBACxE,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,oBAAoB;8BACpB;8BAAGD,mBAAmBI,QAAQ;;aAC/B;YACDX,QAAQrF;QACV;IACF;IAEA,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,IAAI5B,sBAAsB2D,YAAY,IAAI3D,sBAAsBjB,QAAQ,EAAE;QACxE,OAAO;YACLiI,UAAU;gBACRlB;gBACA2B;8BACA,qBAAC5I;oBAASE,UAAUiB,sBAAsBjB,QAAQ;;aACnD;YACDkI,QAAQrF;QACV;IACF;IAEA,MAAMiG,oBAAoBC,IAAAA,kCAAiB,EAAChF;IAE5C,oEAAoE;IACpE,iEAAiE;IACjE,IAAI0D,oBAAoB,CAAC;IACzB,IACEhD,YACA/D,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAACuG,iBAAiBjB,MAAM,EACxB;QACAyB,oBAAoB;YAClBoB,wBACE;;kCACE,qBAACG;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3B7C,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACyC;wBAAKC,MAAK;wBAAaC,SAAQ;;oBAEjCxE;kCACD,qBAACD;;;QAGP;IACF;IAEA,MAAM0E,QAAQ;QACZ,GAAGV,kBAAkB;QACrB,GAAGhB,iBAAiB;QACpB,8GAA8G;QAC9G,gEAAgE;QAChE,+GAA+G;QAC/GrB,QAAQQ;QACR,iCAAiC;QACjC,GAAG,AAAC,CAAA;YACF,IAAIkC,qBAAqB7H,sBAAsBoE,kBAAkB,EAAE;gBACjE,OAAO,CAAC;YACV;YAEA,IAAIvB,QAAQ;gBACV,OAAOjC;YACT;QACF,CAAA,GAAI;IACN;IAEA,OAAO;QACLoG,UAAU;YACRlB;YACA2B;0BACA;;oBACG5E,SAASnD,iBAAiB;oBAE1BmD,UAAUgF,kCACT,qBAACvH;wBACC6H,mBAAmBD;wBACnBxD,WAAWA;wBACXN,oBAAoBpE,sBAAsBoE,kBAAkB;uCAG9D,qBAACM;wBAAW,GAAGwD,KAAK;;oBAUrB;;;SAEJ;QACDjB,QAAQrF;IACV;AACF"}