{"version": 3, "sources": ["../../../src/server/app-render/action-encryption-utils.ts"], "names": ["arrayBufferToString", "stringToUint8Array", "encrypt", "decrypt", "generateRandomActionKeyRaw", "setReferenceManifestsSingleton", "getServerModuleMap", "getClientReferenceManifestSingleton", "getActionEncryptionKey", "__next_loaded_action_key", "__next_internal_development_raw_action_key", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "length", "arr", "charCodeAt", "key", "iv", "data", "crypto", "subtle", "name", "dev", "<PERSON><PERSON>ey", "exported", "exportKey", "b64", "btoa", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "serverActionsManifestSingleton", "Error", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAQgBA,mBAAmB;eAAnBA;;IAkBAC,kBAAkB;eAAlBA;;IAWAC,OAAO;eAAPA;;IAWAC,OAAO;eAAPA;;IAWMC,0BAA0B;eAA1BA;;IAoCNC,8BAA8B;eAA9BA;;IAuBAC,kBAAkB;eAAlBA;;IAsBAC,mCAAmC;eAAnCA;;IAiBMC,sBAAsB;eAAtBA;;;AA1JtB,wFAAwF;AACxF,mCAAmC;AACnC,IAAIC;AACJ,IAAIC;AAEG,SAASV,oBAAoBW,MAAmB;IACrD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEO,SAASlB,mBAAmBkB,MAAc;IAC/C,MAAML,MAAMK,OAAOE,MAAM;IACzB,MAAMC,MAAM,IAAIT,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BE,GAAG,CAACF,EAAE,GAAGD,OAAOI,UAAU,CAACH;IAC7B;IAEA,OAAOE;AACT;AAEO,SAASpB,QAAQsB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAAC1B,OAAO,CAC1B;QACE2B,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,SAASvB,QAAQqB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACzB,OAAO,CAC1B;QACE0B,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,eAAetB,2BAA2B0B,GAAa;IAC5D,mEAAmE;IACnE,4BAA4B;IAC5B,IAAIA,KAAK;QACP,IAAI,OAAOpB,+CAA+C,aAAa;YACrE,OAAOA;QACT;IACF;IAEA,MAAMc,MAAM,MAAMG,OAAOC,MAAM,CAACG,WAAW,CACzC;QACEF,MAAM;QACNR,QAAQ;IACV,GACA,MACA;QAAC;QAAW;KAAU;IAExB,MAAMW,WAAW,MAAML,OAAOC,MAAM,CAACK,SAAS,CAAC,OAAOT;IACtD,MAAMU,MAAMC,KAAKnC,oBAAoBgC;IAErCvB,2BAA2Be;IAC3B,IAAIM,KAAK;QACPpB,6CAA6CwB;IAC/C;IAEA,OAAOA;AACT;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAME,oCAAoCC,OAAOC,GAAG,CAClD;AAGK,SAASjC,+BAA+B,EAC7CkC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAWhB;IACC,aAAa;IACbC,UAAU,CAACN,kCAAkC,GAAG;QAC9CG;QACAC;QACAC;IACF;AACF;AAEO,SAASnC;IACd,MAAMqC,iCAAiC,AAACD,UAAkB,CACxDN,kCACD;IAUD,IAAI,CAACO,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,OAAOD,+BAA+BF,eAAe;AACvD;AAEO,SAASlC;IACd,MAAMoC,iCAAiC,AAACD,UAAkB,CACxDN,kCACD;IAKD,IAAI,CAACO,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,OAAOD,+BAA+BJ,uBAAuB;AAC/D;AAEO,eAAe/B;IACpB,IAAIC,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAMkC,iCAAiC,AAACD,UAAkB,CACxDN,kCACD;IAKD,IAAI,CAACO,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,MAAMC,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CL,+BAA+BH,qBAAqB,CAACS,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,IAAIN,MAAM;IAClB;IAEAnC,2BAA2B,MAAMkB,OAAOC,MAAM,CAACuB,SAAS,CACtD,OACAlD,mBAAmBmD,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAOpC;AACT"}