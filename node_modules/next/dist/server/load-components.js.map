{"version": 3, "sources": ["../../src/server/load-components.ts"], "names": ["loadManifestWithRetries", "evalManifestWithRetries", "loadComponents", "manifestPath", "attempts", "loadManifest", "err", "wait", "evalManifest", "loadClientReferenceManifest", "entryName", "context", "__RSC_MANIFEST", "undefined", "loadComponentsImpl", "distDir", "page", "isAppPath", "DocumentMod", "AppMod", "Promise", "all", "resolve", "then", "requirePage", "hasClientManifest", "endsWith", "buildManifest", "reactLoadableManifest", "clientReferenceManifest", "serverActionsManifest", "join", "BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "replace", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "catch", "setReferenceManifestsSingleton", "serverModuleMap", "createServerModuleMap", "pageName", "ComponentMod", "Component", "interopDefault", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "getTracer", "wrap", "LoadComponentsSpan"], "mappings": ";;;;;;;;;;;;;;;;IAwEsBA,uBAAuB;eAAvBA;;IAmBAC,uBAAuB;eAAvBA;;IAgITC,cAAc;eAAdA;;;2BAtMN;sBACc;yBACO;gCACG;wBACL;4BACS;8BACQ;sBACtB;uCAC0B;6BACT;AA0C/B,eAAeF,wBACpBG,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOC,IAAAA,0BAAY,EAACF;QACtB,EAAE,OAAOG,KAAK;YACZF;YACA,IAAIA,YAAY,GAAG,MAAME;YAEzB,MAAMC,IAAAA,UAAI,EAAC;QACb;IACF;AACF;AAKO,eAAeN,wBACpBE,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOI,IAAAA,0BAAY,EAACL;QACtB,EAAE,OAAOG,KAAK;YACZF;YACA,IAAIA,YAAY,GAAG,MAAME;YAEzB,MAAMC,IAAAA,UAAI,EAAC;QACb;IACF;AACF;AAEA,eAAeE,4BACbN,YAAoB,EACpBO,SAAiB;IAEjB,IAAI;QACF,MAAMC,UAAW,MAAMV,wBAAwBE;QAG/C,OAAOQ,QAAQC,cAAc,CAACF,UAAU;IAC1C,EAAE,OAAOJ,KAAK;QACZ,OAAOO;IACT;AACF;AAEA,eAAeC,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EAKV;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACF,WAAW;QACb,CAACC,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzCD,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,cAAcT,SAAS;YAChEK,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAAMC,IAAAA,oBAAW,EAAC,SAAST,SAAS;SAC5D;IACH;IAEA,6DAA6D;IAC7D,MAAMU,oBACJR,aACCD,CAAAA,KAAKU,QAAQ,CAAC,YAAYV,SAAS,gBAAgBA,SAAS,aAAY;IAE3E,gCAAgC;IAChC,MAAM,CACJW,eACAC,uBACAC,yBACAC,sBACD,GAAG,MAAMV,QAAQC,GAAG,CAAC;QACpBrB,wBACE+B,IAAAA,UAAI,EAAChB,SAASiB,yBAAc;QAE9BhC,wBACE+B,IAAAA,UAAI,EAAChB,SAASkB,kCAAuB;QAEvCR,oBACIhB,4BACEsB,IAAAA,UAAI,EACFhB,SACA,UACA,OACAC,KAAKkB,OAAO,CAAC,QAAQ,OAAO,MAAMC,oCAAyB,GAAG,QAEhEnB,KAAKkB,OAAO,CAAC,QAAQ,QAEvBrB;QACJI,YACKjB,wBACC+B,IAAAA,UAAI,EAAChB,SAAS,UAAUqB,oCAAyB,GAAG,UACpDC,KAAK,CAAC,IAAM,QACd;KACL;IAED,iFAAiF;IACjF,4EAA4E;IAC5E,uCAAuC;IACvC,IAAIP,yBAAyBD,yBAAyB;QACpDS,IAAAA,qDAA8B,EAAC;YAC7BT;YACAC;YACAS,iBAAiBC,IAAAA,kCAAqB,EAAC;gBACrCV;gBACAW,UAAUzB;YACZ;QACF;IACF;IAEA,MAAM0B,eAAe,MAAMtB,QAAQE,OAAO,GAAGC,IAAI,CAAC,IAChDC,IAAAA,oBAAW,EAACR,MAAMD,SAASE;IAG7B,MAAM0B,YAAYC,IAAAA,8BAAc,EAACF;IACjC,MAAMG,WAAWD,IAAAA,8BAAc,EAAC1B;IAChC,MAAM4B,MAAMF,IAAAA,8BAAc,EAACzB;IAE3B,MAAM,EAAE4B,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvER;IAEF,OAAO;QACLI;QACAD;QACAF;QACAhB;QACAC;QACAuB,YAAYT,aAAaU,MAAM,IAAI,CAAC;QACpCV;QACAK;QACAC;QACAC;QACApB;QACAC;QACAb;QACAD;QACAkC;IACF;AACF;AAEO,MAAMhD,iBAAiBmD,IAAAA,iBAAS,IAAGC,IAAI,CAC5CC,8BAAkB,CAACrD,cAAc,EACjCY"}